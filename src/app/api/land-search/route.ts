import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://propbolt.com';

interface LandSearchRequest {
  location: string;
  neLat?: number;
  neLong?: number;
  swLat?: number;
  swLong?: number;
  minPrice?: number;
  maxPrice?: number;
  minAcres?: number;
  maxAcres?: number;
  zoning?: string[];
  utilities?: string[];
  page?: number;
  limit?: number;
}

interface PropertyResult {
  id: string;
  address: string;
  price: number;
  acres?: number;
  zoning?: string;
  latitude: number;
  longitude: number;
  description?: string;
  utilities?: string[];
  source: 'RealEstateAPI' | 'PropBolt';
  details?: any;
}

// Fetch from RealEstateAPI.com via proxy
async function fetchFromRealEstateAPI(searchParams: LandSearchRequest): Promise<PropertyResult[]> {
  try {
    // Build correct RealEstate API request
    const realEstateRequest = {
      city: searchParams.location?.split(',')[0]?.trim() || 'Daytona Beach',
      state: searchParams.location?.split(',')[1]?.trim() || 'FL',
      property_type: 'LAND',
      mls_active: true,
      value_min: searchParams.minPrice,
      value_max: searchParams.maxPrice,
      lot_size_min: searchParams.minAcres ? Math.floor(searchParams.minAcres * 43560) : undefined,
      lot_size_max: searchParams.maxAcres ? Math.floor(searchParams.maxAcres * 43560) : undefined,
      size: searchParams.limit || 10,
      resultIndex: ((searchParams.page || 1) - 1) * (searchParams.limit || 10)
    };

    const response = await fetch(`${API_BASE_URL}/api/v1/proxy/property-search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(realEstateRequest),
    });

    if (!response.ok) {
      console.error('RealEstateAPI error:', response.status, await response.text());
      return [];
    }

    const data = await response.json();
    console.log('RealEstateAPI response:', JSON.stringify(data, null, 2));

    // Handle the actual RealEstate API response format
    const results = data?.data?.results || data?.results || [];

    return results.map((property: any, index: number) => ({
      id: `real-estate-${property.id || property.propertyId || `unknown-${Date.now()}-${index}`}`,
      address: property.address || `${property.city || ''}, ${property.state || ''}`.trim(),
      price: property.value || property.mlsPrice || property.listPrice || 0,
      acres: property.lotSize ? (property.lotSize / 43560).toFixed(2) : property.acres,
      zoning: property.zoning || property.propertyType || 'Unknown',
      latitude: property.latitude || 0,
      longitude: property.longitude || 0,
      description: property.description || `${property.propertyType || 'Land'} property`,
      utilities: property.utilities || [],
      source: 'RealEstateAPI' as const,
      details: property
    }));
  } catch (error) {
    console.error('Error fetching from RealEstateAPI:', error);
    return [];
  }
}

// Fetch from internal PropBolt API using database search (no proxy)
async function fetchFromPropBoltAPI(searchParams: LandSearchRequest): Promise<PropertyResult[]> {
  try {
    // Use database search endpoint (proxy disabled)
    const response = await fetch(`${API_BASE_URL}/api/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: searchParams.location || 'Daytona Beach, FL',
        filters: {
          minPrice: searchParams.minPrice,
          maxPrice: searchParams.maxPrice,
          location: searchParams.location,
          zoning: searchParams.zoning,
          propertyType: 'vacant_land'
        }
      }),
    });

    if (!response.ok) {
      console.error('PropBolt API error:', response.status, await response.text());
      return [];
    }

    const data = await response.json();
    console.log('PropBolt API response:', JSON.stringify(data, null, 2));

    // Handle PropBolt database search response format
    const results = data?.results || [];

    return results.map((property: any, index: number) => ({
      id: `propbolt-db-${property.id || `unknown-${Date.now()}-${index}`}`,
      address: property.address || '',
      price: property.price || 0,
      acres: property.size || 'Unknown',
      zoning: property.zoning || 'Unknown',
      latitude: property.lat || property.latitude || 0,
      longitude: property.lng || property.longitude || 0,
      description: property.description || 'Vacant Land Property',
      utilities: [],
      source: 'PropBolt' as const,
      details: {
        ...property,
        habitability: property.habitability,
        proximity: property.proximity,
        chainLeasePotential: property.chainLeasePotential,
        daysOnMarket: property.daysOnMarket,
        pricePerSqFt: property.pricePerSqFt
      }
    }));
  } catch (error) {
    console.error('Error fetching from PropBolt API:', error);
    return [];
  }
}

// Combine and deduplicate results
function combineResults(realEstateResults: PropertyResult[], propBoltResults: PropertyResult[]): PropertyResult[] {
  const combined = [...realEstateResults, ...propBoltResults];
  const seen = new Set<string>();
  
  return combined.filter(property => {
    const key = `${property.address}-${property.price}`;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check account type
    if (session.user.accountType !== 'land' && session.user.accountType !== 'data') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const searchParams: LandSearchRequest = await request.json();

    // Default to Daytona Beach if no location specified
    if (!searchParams.location) {
      searchParams.location = 'Daytona Beach, FL';
      searchParams.neLat = 29.3;
      searchParams.neLong = -80.9;
      searchParams.swLat = 29.1;
      searchParams.swLong = -81.1;
    }

    // Admin/Land Search: Use RealEstate API + Normal PropBolt API (proxy disabled)
    console.log('Admin/Land Search: Using RealEstate API + Normal PropBolt API (proxy disabled)');

    const [realEstateResults, propBoltResults] = await Promise.all([
      fetchFromRealEstateAPI(searchParams),
      fetchFromPropBoltAPI(searchParams)
    ]);

    // Combine and deduplicate results from both APIs
    const combinedResults = combineResults(realEstateResults, propBoltResults);

    // Apply pagination
    const page = searchParams.page || 1;
    const limit = searchParams.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedResults = combinedResults.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      results: paginatedResults,
      pagination: {
        page,
        limit,
        total: combinedResults.length,
        totalPages: Math.ceil(combinedResults.length / limit)
      },
      sources: {
        realEstateAPI: realEstateResults.length,
        propBolt: propBoltResults.length,
        combined: combinedResults.length
      }
    });

  } catch (error) {
    console.error('Land search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  // Handle GET requests with query parameters
  const searchParams = request.nextUrl.searchParams;
  
  const landSearchRequest: LandSearchRequest = {
    location: searchParams.get('location') || 'Daytona Beach, FL',
    minPrice: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : undefined,
    maxPrice: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : undefined,
    minAcres: searchParams.get('minAcres') ? parseFloat(searchParams.get('minAcres')!) : undefined,
    maxAcres: searchParams.get('maxAcres') ? parseFloat(searchParams.get('maxAcres')!) : undefined,
    page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1,
    limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10,
  };

  // Convert GET to POST request
  return POST(new NextRequest(request.url, {
    method: 'POST',
    body: JSON.stringify(landSearchRequest),
    headers: request.headers,
  }));
}
