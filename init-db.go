package main

import (
	"log"
	"os"
	"propbolt/database"
)

func main() {
	// Set the DATABASE_URL environment variable for local connection via Cloud SQL Proxy
	os.Setenv("DATABASE_URL", "postgresql://propbolt_user:PropboltSecure2024!@127.0.0.1:9470/propbolt?sslmode=disable")
	
	log.Println("🚀 Initializing PropBolt Database...")
	
	// Initialize the database (this will create all tables)
	if err := database.InitDB(); err != nil {
		log.Fatalf("❌ Failed to initialize database: %v", err)
	}
	
	log.Println("✅ Database initialized successfully!")
	log.Println("📊 All tables created and ready for use")
	
	// Close the database connection
	database.CloseDB()
}
