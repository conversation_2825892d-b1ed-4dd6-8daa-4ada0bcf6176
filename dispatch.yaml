dispatch:
  # All API traffic routes to unified api.propbolt.com service
  - url: "api.propbolt.com/*"
    service: default

  # Legacy brain/admin routes redirect to api.propbolt.com
  - url: "brain.propbolt.com/*"
    service: default

  - url: "admin.propbolt.com/*"
    service: default

  - url: "go.propbolt.com/*"
    service: default

  # All API requests route to unified backend
  - url: "propbolt.com/api/*"
    service: default

  - url: "propbolt.com/property*"
    service: default

  - url: "propbolt.com/search*"
    service: default

  - url: "propbolt.com/status*"
    service: default

  - url: "propbolt.com/health*"
    service: default

  - url: "propbolt.com/login*"
    service: default

  # Frontend routes
  - url: "www.propbolt.com/*"
    service: frontend

  - url: "propbolt.com/*"
    service: frontend

  # Catch-all for any API endpoints
  - url: "*/api/*"
    service: default

  # Default frontend routing
  - url: "*/*"
    service: frontend
