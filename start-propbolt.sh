#!/bin/bash

# PropBolt Unified Startup Script
# Starts both backend (Go) and frontend (Next.js) services

set -e

echo "🚀 PropBolt Unified Startup"
echo "=========================="
echo "🏗️ Starting Backend and Frontend Services"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/Users/<USER>/byte-media/v1-go"
BACKEND_PORT="8080"
FRONTEND_PORT="3000"

echo -e "${BLUE}📋 Configuration:${NC}"
echo "  Project Directory: $PROJECT_DIR"
echo "  Backend Port: $BACKEND_PORT"
echo "  Frontend Port: $FRONTEND_PORT"
echo ""

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo -e "${YELLOW}⚠️  Port $port is already in use${NC}"
        echo "Killing existing process on port $port..."
        sudo kill -9 $(lsof -ti:$port) 2>/dev/null || true
        sleep 2
    fi
}

# Function to start backend
start_backend() {
    echo -e "${YELLOW}🔧 Starting Go Backend...${NC}"
    
    # Check dependencies
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go is not installed${NC}"
        exit 1
    fi
    
    # Set environment variables for local development
    export DATABASE_URL="**************************************************************************************************************************"
    export PORT="$BACKEND_PORT"
    export ENVIRONMENT="development"
    
    # Clean dependencies
    echo "📦 Cleaning Go dependencies..."
    go mod tidy
    
    # Check port availability
    check_port $BACKEND_PORT
    
    # Start backend in background
    echo "▶️  Starting Go server on port $BACKEND_PORT..."
    nohup go run main.go > backend.log 2>&1 &
    BACKEND_PID=$!
    
    # Wait for backend to start
    echo "⏳ Waiting for backend to start..."
    sleep 5
    
    # Check if backend is running
    if curl -s http://localhost:$BACKEND_PORT/health > /dev/null; then
        echo -e "${GREEN}✅ Backend started successfully (PID: $BACKEND_PID)${NC}"
        echo "📊 Backend health check: http://localhost:$BACKEND_PORT/health"
    else
        echo -e "${RED}❌ Backend failed to start${NC}"
        echo "📄 Check backend.log for errors"
        exit 1
    fi
}

# Function to start frontend
start_frontend() {
    echo -e "${YELLOW}🎨 Starting Next.js Frontend...${NC}"
    
    # Check dependencies
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm is not installed${NC}"
        exit 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing npm dependencies..."
        npm install
    fi
    
    # Set environment variables
    export NEXT_PUBLIC_API_URL="http://localhost:$BACKEND_PORT"
    export NEXTAUTH_URL="http://localhost:$FRONTEND_PORT"
    export NEXTAUTH_SECRET="development-secret-key"
    export PORT="$FRONTEND_PORT"
    
    # Check port availability
    check_port $FRONTEND_PORT
    
    # Start frontend in background
    echo "▶️  Starting Next.js server on port $FRONTEND_PORT..."
    nohup npm run dev > frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    echo "⏳ Waiting for frontend to start..."
    sleep 10
    
    # Check if frontend is running
    if curl -s http://localhost:$FRONTEND_PORT > /dev/null; then
        echo -e "${GREEN}✅ Frontend started successfully (PID: $FRONTEND_PID)${NC}"
        echo "🌐 Frontend URL: http://localhost:$FRONTEND_PORT"
    else
        echo -e "${RED}❌ Frontend failed to start${NC}"
        echo "📄 Check frontend.log for errors"
        exit 1
    fi
}

# Function to show status
show_status() {
    echo ""
    echo -e "${BLUE}📊 PropBolt Service Status:${NC}"
    echo "=========================="
    
    # Backend status
    if curl -s http://localhost:$BACKEND_PORT/health > /dev/null; then
        echo -e "🔧 Backend: ${GREEN}✅ Running${NC} (http://localhost:$BACKEND_PORT)"
    else
        echo -e "🔧 Backend: ${RED}❌ Not Running${NC}"
    fi
    
    # Frontend status
    if curl -s http://localhost:$FRONTEND_PORT > /dev/null; then
        echo -e "🎨 Frontend: ${GREEN}✅ Running${NC} (http://localhost:$FRONTEND_PORT)"
    else
        echo -e "🎨 Frontend: ${RED}❌ Not Running${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}🔗 Quick Links:${NC}"
    echo "  🏠 Frontend: http://localhost:$FRONTEND_PORT"
    echo "  🔧 Backend API: http://localhost:$BACKEND_PORT"
    echo "  ❤️  Health Check: http://localhost:$BACKEND_PORT/health"
    echo "  📊 API Docs: http://localhost:$BACKEND_PORT/api/docs"
    echo ""
    echo -e "${BLUE}📄 Log Files:${NC}"
    echo "  🔧 Backend: backend.log"
    echo "  🎨 Frontend: frontend.log"
    echo ""
    echo -e "${YELLOW}💡 Commands:${NC}"
    echo "  📄 View backend logs: tail -f backend.log"
    echo "  📄 View frontend logs: tail -f frontend.log"
    echo "  🛑 Stop services: ./stop-propbolt.sh"
}

# Function to create stop script
create_stop_script() {
    cat > stop-propbolt.sh << 'EOF'
#!/bin/bash

echo "🛑 Stopping PropBolt Services..."

# Kill processes on ports
echo "🔧 Stopping backend (port 8080)..."
sudo kill -9 $(lsof -ti:8080) 2>/dev/null || echo "No process on port 8080"

echo "🎨 Stopping frontend (port 3000)..."
sudo kill -9 $(lsof -ti:3000) 2>/dev/null || echo "No process on port 3000"

echo "✅ PropBolt services stopped"
EOF
    chmod +x stop-propbolt.sh
    echo -e "${GREEN}📄 Created stop-propbolt.sh script${NC}"
}

# Main execution
main() {
    echo "🎬 Starting PropBolt services..."
    echo ""
    
    # Change to project directory
    cd "$PROJECT_DIR"
    
    # Start backend first
    start_backend
    echo ""
    
    # Start frontend
    start_frontend
    echo ""
    
    # Create stop script
    create_stop_script
    echo ""
    
    # Show final status
    show_status
    
    echo -e "${GREEN}🎉 PropBolt is now running!${NC}"
    echo -e "${BLUE}🌐 Open your browser to: http://localhost:$FRONTEND_PORT${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  Keep this terminal open or services will stop${NC}"
    echo -e "${YELLOW}💡 Use Ctrl+C to stop all services${NC}"
    
    # Wait for user interrupt
    trap 'echo -e "\n🛑 Stopping PropBolt services..."; ./stop-propbolt.sh; exit 0' INT
    
    # Keep script running
    while true; do
        sleep 30
        # Optional: Add health checks here
    done
}

# Execute main function
main
