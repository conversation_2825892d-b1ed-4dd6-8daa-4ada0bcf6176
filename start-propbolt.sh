#!/bin/bash

# PropBolt Complete API Startup Script
# Starts all 4 PropBolt API services

set -e

echo "🚀 PropBolt Complete API Startup"
echo "================================"
echo "🏗️ Starting All 4 PropBolt API Services"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/Users/<USER>/byte-media/v1-go"
MAIN_API_PORT="8080"        # Main Go Backend API
FRONTEND_API_PORT="3000"    # Next.js Frontend API
DATA_API_PORT="8081"        # Data API Service
PROXY_API_PORT="8082"       # RealEstateAPI Proxy Service

echo -e "${BLUE}📋 PropBolt API Configuration:${NC}"
echo "  Project Directory: $PROJECT_DIR"
echo "  🔧 Main Backend API: Port $MAIN_API_PORT"
echo "  🎨 Frontend API: Port $FRONTEND_API_PORT"
echo "  📊 Data API Service: Port $DATA_API_PORT"
echo "  🔗 RealEstate Proxy API: Port $PROXY_API_PORT"
echo ""

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo -e "${YELLOW}⚠️  Port $port is already in use${NC}"
        echo "Killing existing process on port $port..."
        sudo kill -9 $(lsof -ti:$port) 2>/dev/null || true
        sleep 2
    fi
}

# Function to start main backend API
start_main_api() {
    echo -e "${YELLOW}🔧 Starting Main Go Backend API...${NC}"

    # Check dependencies
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go is not installed${NC}"
        exit 1
    fi

    # Set environment variables for main API
    export DATABASE_URL="**************************************************************************************************************************"
    export PORT="$MAIN_API_PORT"
    export ENVIRONMENT="development"
    export REAL_ESTATE_API_KEY="${REAL_ESTATE_API_KEY:-your-api-key}"
    export PROXY_URLS="http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002"

    # Clean dependencies
    echo "📦 Cleaning Go dependencies..."
    go mod tidy

    # Check port availability
    check_port $MAIN_API_PORT

    # Start main API in background
    echo "▶️  Starting Main API server on port $MAIN_API_PORT..."
    nohup go run main.go > main_api.log 2>&1 &
    MAIN_API_PID=$!

    # Wait for API to start
    echo "⏳ Waiting for Main API to start..."
    sleep 5

    # Check if API is running
    if curl -s http://localhost:$MAIN_API_PORT/health > /dev/null; then
        echo -e "${GREEN}✅ Main API started successfully (PID: $MAIN_API_PID)${NC}"
        echo "📊 Main API health: http://localhost:$MAIN_API_PORT/health"
    else
        echo -e "${RED}❌ Main API failed to start${NC}"
        echo "📄 Check main_api.log for errors"
        exit 1
    fi
}

# Function to start frontend API
start_frontend_api() {
    echo -e "${CYAN}🎨 Starting Next.js Frontend API...${NC}"

    # Check dependencies
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm is not installed${NC}"
        exit 1
    fi

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing npm dependencies..."
        npm install
    fi

    # Set environment variables for frontend
    export NEXT_PUBLIC_API_URL="http://localhost:$MAIN_API_PORT"
    export NEXT_PUBLIC_API_BASE_URL="http://localhost:$MAIN_API_PORT"
    export NEXTAUTH_URL="http://localhost:$FRONTEND_API_PORT"
    export NEXTAUTH_SECRET="development-secret-key-propbolt-2024"
    export PORT="$FRONTEND_API_PORT"

    # Check port availability
    check_port $FRONTEND_API_PORT

    # Start frontend in background
    echo "▶️  Starting Next.js Frontend API on port $FRONTEND_API_PORT..."
    nohup npm run dev > frontend_api.log 2>&1 &
    FRONTEND_API_PID=$!

    # Wait for frontend to start
    echo "⏳ Waiting for Frontend API to start..."
    sleep 10

    # Check if frontend is running
    if curl -s http://localhost:$FRONTEND_API_PORT > /dev/null; then
        echo -e "${GREEN}✅ Frontend API started successfully (PID: $FRONTEND_API_PID)${NC}"
        echo "🌐 Frontend API URL: http://localhost:$FRONTEND_API_PORT"
    else
        echo -e "${RED}❌ Frontend API failed to start${NC}"
        echo "📄 Check frontend_api.log for errors"
        exit 1
    fi
}

# Function to start data API service
start_data_api() {
    echo -e "${PURPLE}📊 Starting Data API Service...${NC}"

    # Create a separate data API service
    cat > data_api_server.go << 'EOF'
package main

import (
    "fmt"
    "log"
    "net/http"
    "os"
    "propbolt/handlers"
    "propbolt/middleware"
    "propbolt/database"
)

func main() {
    // Initialize database
    if err := database.InitDB(); err != nil {
        log.Fatalf("Failed to initialize database: %v", err)
    }
    defer database.CloseDB()

    mux := http.NewServeMux()

    // Data API endpoints with API key authentication
    mux.Handle("/api/v1/data/property", middleware.RateLimit(middleware.APIKeyAuth(http.HandlerFunc(handlers.PropertyHandler))))
    mux.Handle("/api/v1/data/search", middleware.RateLimit(middleware.APIKeyAuth(http.HandlerFunc(handlers.SearchHandler))))
    mux.Handle("/api/v1/data/search/sold", middleware.RateLimit(middleware.APIKeyAuth(http.HandlerFunc(handlers.SoldSearchHandler))))
    mux.Handle("/api/v1/data/search/rentals", middleware.RateLimit(middleware.APIKeyAuth(http.HandlerFunc(handlers.RentalsSearchHandler))))
    mux.Handle("/api/v1/data/property/details", middleware.RateLimit(middleware.APIKeyAuth(http.HandlerFunc(handlers.PropertyDetailsHandler))))
    mux.Handle("/api/v1/data/autocomplete", middleware.RateLimit(middleware.APIKeyAuth(http.HandlerFunc(handlers.AutoCompleteHandler))))
    mux.Handle("/api/v1/data/health", middleware.RateLimit(middleware.APIKeyAuth(http.HandlerFunc(handlers.HealthCheckHandler))))

    // Health check without auth
    mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
        w.WriteHeader(http.StatusOK)
        w.Write([]byte("Data API Service is healthy"))
    })

    port := os.Getenv("DATA_API_PORT")
    if port == "" {
        port = "8081"
    }

    fmt.Printf("Data API Service started on port %s\n", port)
    log.Fatal(http.ListenAndServe(":"+port, mux))
}
EOF

    # Set environment variables for data API
    export DATABASE_URL="**************************************************************************************************************************"
    export DATA_API_PORT="$DATA_API_PORT"
    export ENVIRONMENT="development"

    # Check port availability
    check_port $DATA_API_PORT

    # Start data API in background
    echo "▶️  Starting Data API Service on port $DATA_API_PORT..."
    nohup go run data_api_server.go > data_api.log 2>&1 &
    DATA_API_PID=$!

    # Wait for data API to start
    echo "⏳ Waiting for Data API to start..."
    sleep 5

    # Check if data API is running
    if curl -s http://localhost:$DATA_API_PORT/health > /dev/null; then
        echo -e "${GREEN}✅ Data API started successfully (PID: $DATA_API_PID)${NC}"
        echo "📊 Data API health: http://localhost:$DATA_API_PORT/health"
    else
        echo -e "${RED}❌ Data API failed to start${NC}"
        echo "📄 Check data_api.log for errors"
        exit 1
    fi
}

# Function to start RealEstate proxy API
start_proxy_api() {
    echo -e "${BLUE}🔗 Starting RealEstate Proxy API...${NC}"

    # Create a separate proxy API service
    cat > proxy_api_server.go << 'EOF'
package main

import (
    "fmt"
    "log"
    "net/http"
    "os"
    "propbolt/realestate"
    "propbolt/database"
)

func main() {
    // Initialize database
    if err := database.InitDB(); err != nil {
        log.Fatalf("Failed to initialize database: %v", err)
    }
    defer database.CloseDB()

    mux := http.NewServeMux()

    // RealEstateAPI Proxy Endpoints
    mux.HandleFunc("/api/v1/proxy/autocomplete", func(w http.ResponseWriter, r *http.Request) {
        client := realestate.NewClient()
        // Handle autocomplete proxy
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusOK)
        w.Write([]byte(`{"status": "RealEstate Proxy API - AutoComplete"}`))
    })

    mux.HandleFunc("/api/v1/proxy/property-search", func(w http.ResponseWriter, r *http.Request) {
        client := realestate.NewClient()
        // Handle property search proxy
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusOK)
        w.Write([]byte(`{"status": "RealEstate Proxy API - Property Search"}`))
    })

    mux.HandleFunc("/api/v1/proxy/property-detail", func(w http.ResponseWriter, r *http.Request) {
        client := realestate.NewClient()
        // Handle property detail proxy
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusOK)
        w.Write([]byte(`{"status": "RealEstate Proxy API - Property Detail"}`))
    })

    // Health check
    mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
        w.WriteHeader(http.StatusOK)
        w.Write([]byte("RealEstate Proxy API Service is healthy"))
    })

    port := os.Getenv("PROXY_API_PORT")
    if port == "" {
        port = "8082"
    }

    fmt.Printf("RealEstate Proxy API Service started on port %s\n", port)
    log.Fatal(http.ListenAndServe(":"+port, mux))
}
EOF

    # Set environment variables for proxy API
    export DATABASE_URL="**************************************************************************************************************************"
    export PROXY_API_PORT="$PROXY_API_PORT"
    export REAL_ESTATE_API_KEY="${REAL_ESTATE_API_KEY:-your-api-key}"
    export ENVIRONMENT="development"

    # Check port availability
    check_port $PROXY_API_PORT

    # Start proxy API in background
    echo "▶️  Starting RealEstate Proxy API on port $PROXY_API_PORT..."
    nohup go run proxy_api_server.go > proxy_api.log 2>&1 &
    PROXY_API_PID=$!

    # Wait for proxy API to start
    echo "⏳ Waiting for Proxy API to start..."
    sleep 5

    # Check if proxy API is running
    if curl -s http://localhost:$PROXY_API_PORT/health > /dev/null; then
        echo -e "${GREEN}✅ Proxy API started successfully (PID: $PROXY_API_PID)${NC}"
        echo "🔗 Proxy API health: http://localhost:$PROXY_API_PORT/health"
    else
        echo -e "${RED}❌ Proxy API failed to start${NC}"
        echo "📄 Check proxy_api.log for errors"
        exit 1
    fi
}

# Function to show status of all APIs
show_status() {
    echo ""
    echo -e "${BLUE}📊 PropBolt Complete API Status:${NC}"
    echo "=================================="

    # Main API status
    if curl -s http://localhost:$MAIN_API_PORT/health > /dev/null; then
        echo -e "🔧 Main Backend API: ${GREEN}✅ Running${NC} (http://localhost:$MAIN_API_PORT)"
    else
        echo -e "🔧 Main Backend API: ${RED}❌ Not Running${NC}"
    fi

    # Frontend API status
    if curl -s http://localhost:$FRONTEND_API_PORT > /dev/null; then
        echo -e "🎨 Frontend API: ${GREEN}✅ Running${NC} (http://localhost:$FRONTEND_API_PORT)"
    else
        echo -e "🎨 Frontend API: ${RED}❌ Not Running${NC}"
    fi

    # Data API status
    if curl -s http://localhost:$DATA_API_PORT/health > /dev/null; then
        echo -e "📊 Data API Service: ${GREEN}✅ Running${NC} (http://localhost:$DATA_API_PORT)"
    else
        echo -e "📊 Data API Service: ${RED}❌ Not Running${NC}"
    fi

    # Proxy API status
    if curl -s http://localhost:$PROXY_API_PORT/health > /dev/null; then
        echo -e "🔗 RealEstate Proxy API: ${GREEN}✅ Running${NC} (http://localhost:$PROXY_API_PORT)"
    else
        echo -e "🔗 RealEstate Proxy API: ${RED}❌ Not Running${NC}"
    fi

    echo ""
    echo -e "${BLUE}🔗 API Endpoints:${NC}"
    echo "  🏠 Frontend: http://localhost:$FRONTEND_API_PORT"
    echo "  🔧 Main API: http://localhost:$MAIN_API_PORT"
    echo "  📊 Data API: http://localhost:$DATA_API_PORT/api/v1/data/*"
    echo "  🔗 Proxy API: http://localhost:$PROXY_API_PORT/api/v1/proxy/*"
    echo ""
    echo -e "${BLUE}❤️  Health Checks:${NC}"
    echo "  🔧 Main API: http://localhost:$MAIN_API_PORT/health"
    echo "  📊 Data API: http://localhost:$DATA_API_PORT/health"
    echo "  🔗 Proxy API: http://localhost:$PROXY_API_PORT/health"
    echo ""
    echo -e "${BLUE}📄 Log Files:${NC}"
    echo "  🔧 Main API: main_api.log"
    echo "  🎨 Frontend API: frontend_api.log"
    echo "  📊 Data API: data_api.log"
    echo "  🔗 Proxy API: proxy_api.log"
    echo ""
    echo -e "${YELLOW}💡 Commands:${NC}"
    echo "  📄 View logs: tail -f [service]_api.log"
    echo "  🛑 Stop all APIs: ./stop-propbolt.sh"
}

# Function to create stop script for all APIs
create_stop_script() {
    cat > stop-propbolt.sh << 'EOF'
#!/bin/bash

echo "🛑 Stopping All PropBolt API Services..."

# Kill processes on all ports
echo "🔧 Stopping Main API (port 8080)..."
sudo kill -9 $(lsof -ti:8080) 2>/dev/null || echo "No process on port 8080"

echo "🎨 Stopping Frontend API (port 3000)..."
sudo kill -9 $(lsof -ti:3000) 2>/dev/null || echo "No process on port 3000"

echo "📊 Stopping Data API (port 8081)..."
sudo kill -9 $(lsof -ti:8081) 2>/dev/null || echo "No process on port 8081"

echo "🔗 Stopping Proxy API (port 8082)..."
sudo kill -9 $(lsof -ti:8082) 2>/dev/null || echo "No process on port 8082"

# Clean up temporary files
echo "🧹 Cleaning up temporary files..."
rm -f data_api_server.go proxy_api_server.go

echo "✅ All PropBolt API services stopped"
EOF
    chmod +x stop-propbolt.sh
    echo -e "${GREEN}📄 Created stop-propbolt.sh script for all APIs${NC}"
}

# Main execution
main() {
    echo "🎬 Starting All PropBolt API Services..."
    echo ""

    # Change to project directory
    cd "$PROJECT_DIR"

    # Start all APIs in sequence
    echo -e "${BLUE}🚀 Starting API Services in Order:${NC}"
    echo "1️⃣ Main Backend API"
    echo "2️⃣ Frontend API"
    echo "3️⃣ Data API Service"
    echo "4️⃣ RealEstate Proxy API"
    echo ""

    # Start main API first
    start_main_api
    echo ""

    # Start frontend API
    start_frontend_api
    echo ""

    # Start data API
    start_data_api
    echo ""

    # Start proxy API
    start_proxy_api
    echo ""

    # Create stop script
    create_stop_script
    echo ""

    # Show final status
    show_status

    echo -e "${GREEN}🎉 All 4 PropBolt APIs are now running!${NC}"
    echo -e "${BLUE}🌐 Main Frontend: http://localhost:$FRONTEND_API_PORT${NC}"
    echo -e "${BLUE}🔧 Main API: http://localhost:$MAIN_API_PORT${NC}"
    echo -e "${BLUE}📊 Data API: http://localhost:$DATA_API_PORT${NC}"
    echo -e "${BLUE}🔗 Proxy API: http://localhost:$PROXY_API_PORT${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  Keep this terminal open or all APIs will stop${NC}"
    echo -e "${YELLOW}💡 Use Ctrl+C to stop all API services${NC}"

    # Wait for user interrupt
    trap 'echo -e "\n🛑 Stopping all PropBolt API services..."; ./stop-propbolt.sh; exit 0' INT

    # Keep script running with periodic health checks
    while true; do
        sleep 60
        echo -e "${CYAN}⏰ $(date): All APIs running...${NC}"
    done
}

# Execute main function
main
