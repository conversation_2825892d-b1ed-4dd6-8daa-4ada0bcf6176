#!/bin/bash

# PropBolt Database Schema Installation Script
# Installs all SQL tables in Google Cloud PostgreSQL

set -e

echo "🚀 Installing PropBolt Database Schema"
echo "======================================"

PROJECT_ID="gold-braid-458901-v2"
INSTANCE_NAME="propbolt-postgres"
DATABASE_NAME="propbolt"

echo "📋 Installing complete schema..."

# Install schema using psql through gcloud beta (Cloud SQL proxy)
sudo gcloud beta sql connect $INSTANCE_NAME --user=propbolt_user --database=$DATABASE_NAME --project=$PROJECT_ID << 'EOF'
-- PropBolt Complete Database Schema Installation

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHA<PERSON>(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'viewer',
    account_type VARCHAR(20) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Properties table
CREATE TABLE IF NOT EXISTS properties (
    id SERIAL PRIMARY KEY,
    address VARCHAR(500) NOT NULL UNIQUE,
    price INTEGER NOT NULL,
    size VARCHAR(100),
    zoning VARCHAR(100),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    description TEXT,
    habitability VARCHAR(100),
    proximity VARCHAR(200),
    chain_lease_potential VARCHAR(50),
    days_on_market INTEGER DEFAULT 0,
    price_per_sqft DECIMAL(10, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Watchlist items
CREATE TABLE IF NOT EXISTS watchlist_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, property_id)
);

-- Search queries
CREATE TABLE IF NOT EXISTS search_queries (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    query TEXT,
    filters JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- API Keys
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    key_name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    rate_limit INTEGER DEFAULT 1000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- API Usage
CREATE TABLE IF NOT EXISTS api_usage (
    id SERIAL PRIMARY KEY,
    api_key_id INTEGER REFERENCES api_keys(id) ON DELETE CASCADE,
    endpoint VARCHAR(255) NOT NULL,
    request_count INTEGER DEFAULT 1,
    date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(api_key_id, endpoint, date)
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_properties_zoning ON properties(zoning);
CREATE INDEX IF NOT EXISTS idx_properties_price ON properties(price);
CREATE INDEX IF NOT EXISTS idx_properties_location ON properties(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_watchlist_user ON watchlist_items(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key ON api_keys(api_key);
CREATE INDEX IF NOT EXISTS idx_api_usage_key_date ON api_usage(api_key_id, date);

\q
EOF

echo "✅ Database schema installed successfully!"
echo "📊 Checking tables..."

# Verify installation
sudo gcloud beta sql connect $INSTANCE_NAME --user=propbolt_user --database=$DATABASE_NAME --project=$PROJECT_ID << 'EOF'
\dt
\q
EOF

echo "🎉 PropBolt database is ready!"
