#!/bin/bash

# PropBolt Unified API Startup Script
# Starts the single unified api.propbolt.com backend + frontend

set -e

echo "🚀 PropBolt Unified API Startup"
echo "==============================="
echo "🏗️ Starting api.propbolt.com + Frontend"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/Users/<USER>/byte-media/v1-go"
API_PORT="8080"         # Unified API Backend
FRONTEND_PORT="3000"    # Next.js Frontend

echo -e "${BLUE}📋 PropBolt Unified Configuration:${NC}"
echo "  Project Directory: $PROJECT_DIR"
echo "  🔧 Unified API Backend: Port $API_PORT (api.propbolt.com)"
echo "  🎨 Frontend: Port $FRONTEND_PORT (propbolt.com)"
echo ""
echo -e "${GREEN}✨ All APIs Consolidated into Single Backend:${NC}"
echo "  • Core Property APIs (/property, /search, etc.)"
echo "  • RealEstate Proxy APIs (/api/v1/proxy/*)"
echo "  • Data APIs with API Key Auth (/api/v1/data/*)"
echo "  • User Management APIs (/api/v1/user/*)"
echo "  • Dashboard APIs (/api/*)"
echo "  • Property Analysis (/api/property-analysis)"
echo "  • Authentication (/api/auth/*)"
echo ""

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo -e "${YELLOW}⚠️  Port $port is already in use${NC}"
        echo "Killing existing process on port $port..."
        sudo kill -9 $(lsof -ti:$port) 2>/dev/null || true
        sleep 2
    fi
}

# Function to start unified API backend
start_unified_api() {
    echo -e "${YELLOW}🔧 Starting Unified API Backend...${NC}"
    
    # Check dependencies
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go is not installed${NC}"
        exit 1
    fi
    
    # Set environment variables for production
    export DATABASE_URL="**************************************************************************************************************************"
    export PORT="$API_PORT"
    export ENVIRONMENT="production"
    export REAL_ESTATE_API_KEY="${REAL_ESTATE_API_KEY:-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914}"
    export PROXY_URLS="http://sp0o8xf1er:<EMAIL>:10001,http://sp0o8xf1er:<EMAIL>:10002"
    export PRODUCTION_DOMAIN="propbolt.com"
    export GCP_PROJECT_ID="gold-braid-458901-v2"
    
    # Clean dependencies
    echo "📦 Cleaning Go dependencies..."
    go mod tidy
    
    # Check port availability
    check_port $API_PORT
    
    # Start unified API in background
    echo "▶️  Starting Unified API server on port $API_PORT..."
    nohup go run main.go > unified_api.log 2>&1 &
    API_PID=$!
    
    # Wait for API to start
    echo "⏳ Waiting for Unified API to start..."
    sleep 8
    
    # Check if API is running
    if curl -s http://localhost:$API_PORT/ > /dev/null; then
        echo -e "${GREEN}✅ Unified API started successfully (PID: $API_PID)${NC}"
        echo "📊 API health: http://localhost:$API_PORT/"
        echo "🔧 API status: http://localhost:$API_PORT/status"
    else
        echo -e "${RED}❌ Unified API failed to start${NC}"
        echo "📄 Check unified_api.log for errors"
        exit 1
    fi
}

# Function to start frontend
start_frontend() {
    echo -e "${CYAN}🎨 Starting Next.js Frontend...${NC}"
    
    # Check dependencies
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm is not installed${NC}"
        exit 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing npm dependencies..."
        npm install
    fi
    
    # Set environment variables for frontend
    export NEXT_PUBLIC_API_URL="http://localhost:$API_PORT"
    export NEXT_PUBLIC_API_BASE_URL="http://localhost:$API_PORT"
    export NEXTAUTH_URL="http://localhost:$FRONTEND_PORT"
    export NEXTAUTH_SECRET="production-secret-key-propbolt-2024"
    export PORT="$FRONTEND_PORT"
    
    # Check port availability
    check_port $FRONTEND_PORT
    
    # Start frontend in background
    echo "▶️  Starting Next.js Frontend on port $FRONTEND_PORT..."
    nohup npm run dev > frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    echo "⏳ Waiting for Frontend to start..."
    sleep 12
    
    # Check if frontend is running
    if curl -s http://localhost:$FRONTEND_PORT > /dev/null; then
        echo -e "${GREEN}✅ Frontend started successfully (PID: $FRONTEND_PID)${NC}"
        echo "🌐 Frontend URL: http://localhost:$FRONTEND_PORT"
    else
        echo -e "${RED}❌ Frontend failed to start${NC}"
        echo "📄 Check frontend.log for errors"
        exit 1
    fi
}

# Function to show status
show_status() {
    echo ""
    echo -e "${BLUE}📊 PropBolt Unified System Status:${NC}"
    echo "=================================="
    
    # API status
    if curl -s http://localhost:$API_PORT/ > /dev/null; then
        echo -e "🔧 Unified API Backend: ${GREEN}✅ Running${NC} (http://localhost:$API_PORT)"
    else
        echo -e "🔧 Unified API Backend: ${RED}❌ Not Running${NC}"
    fi
    
    # Frontend status
    if curl -s http://localhost:$FRONTEND_PORT > /dev/null; then
        echo -e "🎨 Frontend: ${GREEN}✅ Running${NC} (http://localhost:$FRONTEND_PORT)"
    else
        echo -e "🎨 Frontend: ${RED}❌ Not Running${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}🔗 Unified API Endpoints:${NC}"
    echo "  🏠 Frontend: http://localhost:$FRONTEND_PORT"
    echo "  🔧 Unified API: http://localhost:$API_PORT"
    echo "  📊 All Data APIs: http://localhost:$API_PORT/api/v1/data/*"
    echo "  🔗 All Proxy APIs: http://localhost:$API_PORT/api/v1/proxy/*"
    echo "  👤 User APIs: http://localhost:$API_PORT/api/v1/user/*"
    echo "  🏠 Dashboard APIs: http://localhost:$API_PORT/api/*"
    echo ""
    echo -e "${BLUE}❤️  Health Checks:${NC}"
    echo "  🔧 API Health: http://localhost:$API_PORT/"
    echo "  📊 API Status: http://localhost:$API_PORT/status"
    echo ""
    echo -e "${BLUE}📄 Log Files:${NC}"
    echo "  🔧 Unified API: unified_api.log"
    echo "  🎨 Frontend: frontend.log"
    echo ""
    echo -e "${YELLOW}💡 Commands:${NC}"
    echo "  📄 View API logs: tail -f unified_api.log"
    echo "  📄 View frontend logs: tail -f frontend.log"
    echo "  🛑 Stop services: ./stop-unified.sh"
}

# Function to create stop script
create_stop_script() {
    cat > stop-unified.sh << 'EOF'
#!/bin/bash

echo "🛑 Stopping PropBolt Unified Services..."

# Kill processes on ports
echo "🔧 Stopping Unified API (port 8080)..."
sudo kill -9 $(lsof -ti:8080) 2>/dev/null || echo "No process on port 8080"

echo "🎨 Stopping Frontend (port 3000)..."
sudo kill -9 $(lsof -ti:3000) 2>/dev/null || echo "No process on port 3000"

echo "✅ PropBolt unified services stopped"
EOF
    chmod +x stop-unified.sh
    echo -e "${GREEN}📄 Created stop-unified.sh script${NC}"
}

# Main execution
main() {
    echo "🎬 Starting PropBolt Unified System..."
    echo ""
    
    # Change to project directory
    cd "$PROJECT_DIR"
    
    # Start unified API first
    start_unified_api
    echo ""
    
    # Start frontend
    start_frontend
    echo ""
    
    # Create stop script
    create_stop_script
    echo ""
    
    # Show final status
    show_status
    
    echo -e "${GREEN}🎉 PropBolt Unified System is now running!${NC}"
    echo -e "${BLUE}🌐 Frontend: http://localhost:$FRONTEND_PORT${NC}"
    echo -e "${BLUE}🔧 Unified API: http://localhost:$API_PORT${NC}"
    echo -e "${BLUE}📡 All APIs available at: api.propbolt.com${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  Keep this terminal open or services will stop${NC}"
    echo -e "${YELLOW}💡 Use Ctrl+C to stop all services${NC}"
    
    # Wait for user interrupt
    trap 'echo -e "\n🛑 Stopping PropBolt unified services..."; ./stop-unified.sh; exit 0' INT
    
    # Keep script running with periodic health checks
    while true; do
        sleep 60
        echo -e "${CYAN}⏰ $(date): Unified system running...${NC}"
    done
}

# Execute main function
main
