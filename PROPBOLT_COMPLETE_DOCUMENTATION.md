# PropBolt Complete Documentation

## 🏗️ Project Overview

PropBolt is a comprehensive real estate platform focused on vacant land search and analysis, built with Go backend and Next.js frontend, deployed on Google Cloud Platform.

### Architecture
- **Backend**: Go with PostgreSQL database
- **Frontend**: Next.js with NextAuth authentication
- **Database**: Google Cloud SQL PostgreSQL
- **Deployment**: Google App Engine
- **Domain**: propbolt.com

## 🗄️ Database Schema

### Core Tables
- **users**: Authentication and user management
- **properties**: Vacant land properties
- **api_keys**: API key management for data access
- **api_usage**: API usage tracking and rate limiting
- **watchlist_items**: User saved properties
- **search_queries**: User saved searches

### Database Connection
```
DATABASE_URL: "**************************************************************************************************************************"
```

## 🔑 Authentication System

### Account Types
- **land**: Access to land search dashboard at propbolt.com/land
- **data**: Access to API dashboard at propbolt.com/access
- **admin**: Full administrative access
- **NULL**: No access (login denied)

### User Creation
Use `create-user.sh` script or Go command:
```bash
./create-user.sh
# OR
go run cmd/create-user/main.go
```

## 🌐 API Endpoints

### Public Endpoints
- `GET /health` - Health check
- `GET /api/health` - API health check

### Authentication Required
- `GET /api/v1/user/api-keys` - List user API keys
- `POST /api/v1/user/api-keys/create` - Create new API key
- `DELETE /api/v1/user/api-keys/delete` - Delete API key

### Data API (API Key Required)
- `GET /api/v1/data/property` - Property details
- `GET /api/v1/data/search` - Property search
- `GET /api/v1/data/autocomplete` - Location autocomplete

## 🏞️ Land Search Features

### Search Parameters
- Location (city, state, zip, county)
- Price range (min/max)
- Lot size and acreage
- Property type (land, vacant_land)
- Zoning information
- Utilities availability
- Buildable status
- Waterfront properties

### Integration
- **RealEstateAPI.com**: Primary data source
- **Internal API**: PropBolt-specific data
- **Proxy Support**: Smartproxy for rate limiting

## 🚀 Deployment

### Prerequisites
```bash
# Install dependencies
npm install
go mod tidy

# Set up Google Cloud
gcloud auth login
gcloud config set project gold-braid-458901-v2
```

### Database Setup
```bash
# Install all SQL tables
sudo ./install-all-sql.sh
```

### Production Deployment
```bash
# Deploy backend and frontend
sudo ./deploy-production.sh
```

## 🔧 Development

### Environment Variables
```bash
DATABASE_URL="postgresql://..."
REAL_ESTATE_API_KEY="your-api-key"
NEXTAUTH_SECRET="your-secret"
NEXTAUTH_URL="https://propbolt.com"
```

### Local Development
```bash
# Backend
go run main.go

# Frontend
npm run dev
```

## 📊 Testing

### API Testing
```bash
# Run comprehensive endpoint tests
./run_endpoint_tests.sh
```

### API Key Testing
```bash
# Test API key functionality
./scripts/test_api_keys.sh
```

## 🔒 SSL Certificates

### Manage SSL for all domains
```bash
# Create certificates
./ssl-certificates.sh create

# Check status
./ssl-certificates.sh check
```

## 📈 Monitoring

### Google Cloud Services
- **Logging**: Cloud Logging
- **Monitoring**: Cloud Monitoring
- **Auto-scaling**: App Engine automatic scaling
- **Load Balancing**: Built-in App Engine load balancing

### Health Checks
- Backend: `/health`
- Database: Connection monitoring
- API: Rate limiting and usage tracking

## 🛠️ Maintenance

### Regular Tasks
1. Monitor API usage and rate limits
2. Check SSL certificate status
3. Review database performance
4. Update property data
5. Monitor user activity

### Backup Strategy
- **Database**: Automatic daily backups via Cloud SQL
- **Code**: Git repository backup
- **Configuration**: Environment variable backup

## 📞 Support

### Key Files
- `PROPBOLT_COMPLETE_DOCUMENTATION.md` - This file
- `API_DOCUMENTATION.md` - Detailed API reference
- `create-user.sh` - User creation script
- `deploy-production.sh` - Deployment script
- `ssl-certificates.sh` - SSL management

### Troubleshooting
1. Check health endpoints
2. Review Cloud Logging
3. Verify database connectivity
4. Test API key functionality
5. Monitor rate limits

---

**PropBolt** - Comprehensive Vacant Land Search Platform
*Built for maximum land search capability in Daytona Beach, FL and beyond*
